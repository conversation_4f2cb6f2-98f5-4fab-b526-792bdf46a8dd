{application,mint,
             [{modules,['Elixir.Mint.Application','Elixir.Mint.Core.Conn',
                        'Elixir.Mint.Core.Headers',
                        'Elixir.Mint.Core.Transport',
                        'Elixir.Mint.Core.Transport.SSL',
                        'Elixir.Mint.Core.Transport.TCP',
                        'Elixir.Mint.Core.Util','Elixir.Mint.HTTP',
                        'Elixir.Mint.HTTP1','Elixir.Mint.HTTP1.Parse',
                        'Elixir.Mint.HTTP1.Request',
                        'Elixir.Mint.HTTP1.Response','Elixir.Mint.HTTP2',
                        'Elixir.Mint.HTTP2.Frame','Elixir.Mint.HTTPError',
                        'Elixir.Mint.Negotiate','Elixir.Mint.TransportError',
                        'Elixir.Mint.TunnelProxy','Elixir.Mint.Types',
                        'Elixir.Mint.UnsafeProxy',mint_shims]},
              {optional_applications,[castore]},
              {applications,[kernel,stdlib,elixir,logger,ssl,castore,hpax]},
              {description,"Small and composable HTTP client."},
              {registered,[]},
              {vsn,"1.7.1"},
              {mod,{'Elixir.Mint.Application',[]}}]}.
