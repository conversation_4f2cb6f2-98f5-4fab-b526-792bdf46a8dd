{application,nimble_options,
             [{modules,['Elixir.NimbleOptions','Elixir.NimbleOptions.Docs',
                        'Elixir.NimbleOptions.ValidationError']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"A tiny library for validating and documenting high-level options"},
              {registered,[]},
              {vsn,"1.1.1"}]}.
